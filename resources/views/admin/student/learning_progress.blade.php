@extends('layouts.admin')
@push('title', '<PERSON><PERSON> trình học của ' . $student->name)
@push('meta')@endpush
@push('css')
<style>
    .progress-card {
        border: 1px solid #e3e6f0;
        border-radius: 8px;
        margin-bottom: 20px;
        transition: all 0.3s ease;
    }
    .progress-card:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }
    .course-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 8px 8px 0 0;
    }
    .progress-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        padding: 20px;
        background: #f8f9fc;
    }
    .stat-item {
        text-align: center;
        padding: 15px;
        background: white;
        border-radius: 6px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #5a5c69;
    }
    .stat-label {
        font-size: 12px;
        color: #858796;
        text-transform: uppercase;
        margin-top: 5px;
    }
    .lesson-list {
        max-height: 400px;
        overflow-y: auto;
    }
    .lesson-item {
        display: flex;
        align-items: center;
        padding: 12px 20px;
        border-bottom: 1px solid #e3e6f0;
        transition: background-color 0.2s;
    }
    .lesson-item:hover {
        background-color: #f8f9fc;
    }
    .lesson-status {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        margin-right: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
    }
    .lesson-completed {
        background-color: #1cc88a;
        color: white;
    }
    .lesson-in-progress {
        background-color: #f6c23e;
        color: white;
    }
    .lesson-not-started {
        background-color: #e3e6f0;
        color: #5a5c69;
    }
    .lesson-info {
        flex: 1;
    }
    .lesson-title {
        font-weight: 500;
        color: #5a5c69;
        margin-bottom: 2px;
    }
    .lesson-duration {
        font-size: 12px;
        color: #858796;
    }
    .lesson-progress {
        width: 100px;
        margin-left: 15px;
    }
    .section-header {
        background-color: #e3e6f0;
        padding: 10px 20px;
        font-weight: 600;
        color: #5a5c69;
        border-bottom: 1px solid #d1d3e2;
    }
    .overall-progress {
        position: relative;
        height: 8px;
        background-color: #e3e6f0;
        border-radius: 4px;
        overflow: hidden;
        margin: 10px 0;
    }
    .overall-progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #1cc88a 0%, #36b9cc 100%);
        transition: width 0.5s ease;
    }
    .no-courses {
        text-align: center;
        padding: 60px 20px;
        color: #858796;
    }
    .back-button {
        margin-bottom: 20px;
    }
    .summary-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 30px;
    }
    .summary-card {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        text-align: center;
        border-left: 4px solid #667eea;
    }
    .summary-card.completed {
        border-left-color: #1cc88a;
    }
    .summary-card.in-progress {
        border-left-color: #f6c23e;
    }
    .summary-card.total-time {
        border-left-color: #36b9cc;
    }
    .summary-number {
        font-size: 32px;
        font-weight: bold;
        color: #5a5c69;
        margin-bottom: 5px;
    }
    .summary-label {
        color: #858796;
        font-size: 14px;
    }
    .filter-section {
        background: white;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .course-search {
        max-width: 300px;
    }
</style>
@endpush

@section('content')
<div class="ol-card radius-8px">
    <div class="ol-card-body my-3 py-12px px-20px">
        <div class="d-flex align-items-center justify-content-between gap-3 flex-wrap flex-md-nowrap">
            <h4 class="title fs-16px">
                <i class="fi-rr-chart-line-up me-2"></i>
                Lộ trình học của {{ $student->name }}
            </h4>
            <a href="{{ route('admin.student.index') }}" class="btn ol-btn-outline-secondary d-flex align-items-center cg-10px">
                <span class="fi-rr-arrow-left"></span>
                <span>Quay lại danh sách học viên</span>
            </a>
        </div>
    </div>
</div>

<!-- Tổng quan -->
@if(count($learning_progress) > 0)
    @php
        $total_courses = count($learning_progress);
        $completed_courses = 0;
        $purchased_courses = 0;
        $not_purchased_courses = 0;
        $total_time_watched = 0;
        $total_time_all = 0;

        foreach($learning_progress as $progress) {
            if($progress['overall_percentage'] >= 100) $completed_courses++;
            if($progress['enrollment']) {
                $purchased_courses++;
            } else {
                $not_purchased_courses++;
            }
            $total_time_watched += $progress['total_watched_seconds'];
            $total_time_all += $progress['total_duration_seconds'];
        }

        $overall_completion = $total_time_all > 0 ? round(($total_time_watched / $total_time_all) * 100, 1) : 0;
    @endphp

    <div class="summary-cards">
        <div class="summary-card">
            <div class="summary-number">{{ $total_courses }}</div>
            <div class="summary-label">Tổng khóa học đã xem</div>
        </div>
        <div class="summary-card" style="border-left-color: #28a745;">
            <div class="summary-number">{{ $purchased_courses }}</div>
            <div class="summary-label">Khóa học đã mua</div>
        </div>
        <div class="summary-card" style="border-left-color: #ffc107;">
            <div class="summary-number">{{ $not_purchased_courses }}</div>
            <div class="summary-label">Khóa học chưa mua</div>
        </div>
        <div class="summary-card completed">
            <div class="summary-number">{{ $completed_courses }}</div>
            <div class="summary-label">Khóa học hoàn thành</div>
        </div>
        <div class="summary-card in-progress">
            <div class="summary-number">{{ $overall_completion }}%</div>
            <div class="summary-label">Tiến độ tổng thể</div>
        </div>
        <div class="summary-card total-time">
            <div class="summary-number">{{ gmdate('H:i', $total_time_watched) }}</div>
            <div class="summary-label">Tổng thời gian học</div>
        </div>
    </div>

    <!-- Bộ lọc -->
    <div class="filter-section">
        <div class="row align-items-center">
            <div class="col-md-6">
                <input type="text" class="form-control course-search" id="courseSearch"
                       placeholder="Tìm kiếm khóa học...">
            </div>
            <div class="col-md-6">
                <div class="d-flex gap-2 justify-content-end">
                    <button class="btn btn-outline-primary btn-sm active" data-filter="all">Tất cả</button>
                    <button class="btn btn-outline-success btn-sm" data-filter="purchased">Đã mua</button>
                    <button class="btn btn-outline-warning btn-sm" data-filter="not-purchased">Chưa mua</button>
                </div>
            </div>
        </div>
    </div>
@endif

<div class="ol-card p-4">
    <div class="ol-card-body">
        @if(count($learning_progress) > 0)
            @foreach($learning_progress as $progress)
                @php
                    $course_status = 'not-started';
                    if($progress['overall_percentage'] >= 100) {
                        $course_status = 'completed';
                    } elseif($progress['overall_percentage'] > 0) {
                        $course_status = 'in-progress';
                    }
                @endphp
                <div class="progress-card"
                     data-course-status="{{ $course_status }}"
                     data-course-title="{{ strtolower($progress['course']->title) }}"
                     data-purchase-status="{{ $progress['enrollment'] ? 'purchased' : 'not-purchased' }}">
                    <div class="course-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="d-flex align-items-center gap-2 mb-1">
                                    <h5 class="mb-0">{{ $progress['course']->title }}</h5>
                                    @if($progress['enrollment'])
                                        <span class="badge bg-success">Đã mua</span>
                                    @else
                                        <span class="badge bg-warning text-dark">Chưa mua</span>
                                    @endif
                                </div>
                                <p class="mb-0 opacity-75">
                                    @if($progress['enrollment'])
                                        Đăng ký: {{ date('d/m/Y', $progress['enrollment']->entry_date) }}
                                    @else
                                        Bắt đầu xem: {{ date('d/m/Y', strtotime($progress['first_watch_date'])) }}
                                    @endif
                                </p>
                            </div>
                            <div class="text-end">
                                <div class="fs-24px fw-bold">{{ $progress['overall_percentage'] }}%</div>
                                <div class="small">Hoàn thành</div>
                            </div>
                        </div>
                        <div class="overall-progress">
                            <div class="overall-progress-bar" style="width: {{ $progress['overall_percentage'] }}%"></div>
                        </div>
                    </div>

                    <div class="progress-stats">
                        <div class="stat-item">
                            <div class="stat-number">{{ $progress['completed_lessons_count'] }}/{{ $progress['total_lessons'] }}</div>
                            <div class="stat-label">Bài học hoàn thành</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{{ gmdate('H:i:s', $progress['total_watched_seconds']) }}</div>
                            <div class="stat-label">Thời gian đã học</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{{ gmdate('H:i:s', $progress['total_duration_seconds']) }}</div>
                            <div class="stat-label">Tổng thời lượng</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{{ $progress['course_progress'] }}%</div>
                            <div class="stat-label">Tiến độ hệ thống</div>
                        </div>
                    </div>

                    <div class="lesson-list">
                        @foreach($progress['sections'] as $section)
                            <div class="section-header">
                                <i class="fi-rr-folder me-2"></i>
                                {{ $section->title }}
                            </div>
                            @foreach($section->lessons as $lesson)
                                @php
                                    $lesson_data = $progress['lesson_progress'][$lesson->id] ?? null;
                                    $is_completed = in_array($lesson->id, $progress['completed_lessons']);
                                    $percentage = $lesson_data ? $lesson_data['percentage'] : 0;

                                    if ($is_completed) {
                                        $status_class = 'lesson-completed';
                                        $status_icon = '✓';
                                    } elseif ($percentage > 0) {
                                        $status_class = 'lesson-in-progress';
                                        $status_icon = '▶';
                                    } else {
                                        $status_class = 'lesson-not-started';
                                        $status_icon = '○';
                                    }
                                @endphp
                                <div class="lesson-item">
                                    <div class="lesson-status {{ $status_class }}">
                                        {{ $status_icon }}
                                    </div>
                                    <div class="lesson-info">
                                        <div class="lesson-title">{{ $lesson->title }}</div>
                                        <div class="lesson-duration">
                                            @if($lesson_data)
                                                {{ gmdate('H:i:s', $lesson_data['watched_seconds']) }} / {{ $lesson->duration }}
                                            @else
                                                Chưa xem / {{ $lesson->duration }}
                                            @endif
                                        </div>
                                    </div>
                                    <div class="lesson-progress">
                                        <div class="progress" style="height: 6px;">
                                            <div class="progress-bar bg-success"
                                                 style="width: {{ $percentage }}%"
                                                 title="{{ $percentage }}%">
                                            </div>
                                        </div>
                                        <div class="text-center small mt-1">{{ round($percentage, 1) }}%</div>
                                    </div>
                                </div>
                            @endforeach
                        @endforeach
                    </div>
                </div>
            @endforeach
        @else
            <div class="no-courses">
                <i class="fi-rr-book-open" style="font-size: 48px; color: #d1d3e2;"></i>
                <h5 class="mt-3">Chưa có lịch sử xem</h5>
                <p>Học viên này chưa xem khóa học nào.</p>
            </div>
        @endif
    </div>
</div>
@endsection

@push('js')
<script>
    // Animation for progress bars
    document.addEventListener('DOMContentLoaded', function() {
        const progressBars = document.querySelectorAll('.overall-progress-bar, .progress-bar');
        progressBars.forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.width = width;
            }, 100);
        });

        // Search functionality
        const searchInput = document.getElementById('courseSearch');
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const courseCards = document.querySelectorAll('.progress-card');

                courseCards.forEach(card => {
                    const courseTitle = card.getAttribute('data-course-title');
                    if (courseTitle.includes(searchTerm)) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        }

        // Filter functionality
        const filterButtons = document.querySelectorAll('[data-filter]');
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Update active button
                filterButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');

                const filter = this.getAttribute('data-filter');
                const courseCards = document.querySelectorAll('.progress-card');

                courseCards.forEach(card => {
                    const courseStatus = card.getAttribute('data-course-status');
                    const purchaseStatus = card.getAttribute('data-purchase-status');

                    let shouldShow = false;

                    if (filter === 'all') {
                        shouldShow = true;
                    } else if (filter === 'purchased' || filter === 'not-purchased') {
                        shouldShow = purchaseStatus === filter;
                    } else {
                        shouldShow = courseStatus === filter;
                    }

                    card.style.display = shouldShow ? 'block' : 'none';
                });
            });
        });
    });
</script>
@endpush
